# Setup
@host = http://localhost:8082

### TODO test to list services for dashboard
## @name LIST SERVICES
#GET {{host}}/dashboard/services
#Authorization: Bearer jwt_....
#
#> {%
#    client.test("Response status should be 200", () => {
#        client.assert(response.status === 200, `Response status was: ${response.status}`)
#    })
#%}

###
# @name LIST SERVICES as ios
GET {{host}}/mobile/services
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222
X-Device-Id: dvc_00000000000000000000000000

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name LIST SERVICES as android
GET {{host}}/mobile/services
Authorization: Bearer dev_44444444444444444444444444444444444444444444444444
X-Device-Id: dvc_00000000000000000000000000
X-App-Platform: android

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name CREATE SERVICE as demo.appio.so
POST {{host}}/demo-appio-so/services
Authorization: Bearer dev_11111111111111111111111111111111111111111111111111

{
  "user_id": "usr_00000000000000000000000000",
  "title": "Demo title",
  "description": "Demo description",
  "banner_url": "https://cdn.appio.so/app/appio.so/banner.jpg",
  "logo_url": "https://cdn.appio.so/app/appio.so/logo.png",
  "text_color": "#FFFFFF",
  "background_color": "#007aFf"
}

> {%
    client.global.set("demo_service_id", response.body.id);
    client.test("Response status should be 201", () => {
        client.assert(response.status === 201, `Response status was: ${response.status}`)
    })
%}

###
# @name GET SERVICE
GET {{host}}/v1/services/svc_00000000000000000000000000
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name GET SERVICE - service does not match API key
GET {{host}}/v1/services/svc_00000000xxxxxxxxxxxxxxxxxx
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

> {%
    client.test("Response status should be 400", () => {
        client.assert(response.status === 400, `Response status was: ${response.status}`)
    })
%}


###
# @name GET SERVICE as app.appio.so
GET {{host}}/app-appio-so/services/{{demo_service_id}}
Authorization: Bearer dev_33333333333333333333333333333333333333333333333333
X-Service-Id: {{demo_service_id}}

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name GET SERVICE as app.appio.so - not found
GET {{host}}/app-appio-so/services/svc_00000000xxxxxxxxxxxxxxxxxx
Authorization: Bearer dev_33333333333333333333333333333333333333333333333333
X-Service-Id: svc_00000000xxxxxxxxxxxxxxxxxx

> {%
    client.test("Response status should be 404", () => {
        client.assert(response.status === 404, `Response status was: ${response.status}`)
    })
%}

###
# @name GET SERVICE as demo.appio.so
GET {{host}}/demo-appio-so/services/{{demo_service_id}}
Authorization: Bearer dev_11111111111111111111111111111111111111111111111111
X-Service-Id: {{demo_service_id}}

> {%
    client.test("Response status should be 200 or 404", () => {
        client.assert([200, 404].includes(response.status), `Response status was: ${response.status}`)
    })
%}

###
# @name GET SERVICE as demo.appio.so
GET {{host}}/demo-appio-so/services/demo_svc_01jrw63pyet5d1htndrc7vva2w
Authorization: Bearer dev_11111111111111111111111111111111111111111111111111
X-Service-Id: demo_svc_01jrw63pyet5d1htndrc7vva2w

> {%
    client.test("Response status should be 200 or 404", () => {
        client.assert([200, 404].includes(response.status), `Response status was: ${response.status}`)
    })
%}

###
# @name GET SERVICE as ios
GET {{host}}/mobile/services/{{demo_service_id}}
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222
X-Service-Id: {{demo_service_id}}
#X-Device-Id: dvc_00000000000000000000000000   # not needed, but client might send it if it has it

> {%
    client.test("Response status should be 200 or 404", () => {
        client.assert([200, 404].includes(response.status), `Response status was: ${response.status}`)
    })
%}

###
# @name UPDATE SERVICE
PATCH {{host}}/v1/services/svc_00000000000000000000000000
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

{
  "title": "new title. {{$uuid}}",
  "description": "new description. {{$isoTimestamp}}",
  "text_color": "#333333",
  "background_color": "#F0F0F0"
}

> {%
    client.test("Response status should be 200 or 404", () => {
        client.assert([200, 404].includes(response.status), `Response status was: ${response.status}`)
    })
%}

###
# @name DELETE SERVICE
# n/a

###