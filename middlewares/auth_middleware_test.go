package middlewares

import (
	"api.appio.so/internal/mocks"
	"api.appio.so/pkg/config"
	"api.appio.so/pkg/roles"
	"api.appio.so/services"
	"context"
	"encoding/json"
	"github.com/appio-so/go-appioid"
	"github.com/jackc/pgx/v5"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"go.uber.org/zap/zaptest"
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestAuthRoleService(t *testing.T) {
	logger := zaptest.NewLogger(t)

	// Mock handler that checks if the context has the expected values
	mockHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("success"))
	})

	t.Run("Success - valid API key with service ID", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		svcID := appioid.MustParse("svc_00000000000000000000000001")

		// Mock repository to return service ID for valid API key
		mockRepo.EXPECT().
			GetServiceIDByAPIKey(gomock.Any(), "valid-api-key_12345678901234567890123456789012345678901234567890").
			Return(svcID, nil)

		authConfig := &config.AuthConfig{
			App:  "app-key",
			IOS:  "ios-key",
			Demo: "demo-key",
		}

		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)
		middleware := AuthRoleService(apiKeyService, logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer valid-api-key_12345678901234567890123456789012345678901234567890")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("Success - valid API key without service ID", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)

		authConfig := &config.AuthConfig{
			App:  "app-key_12345678901234567890123456789012345678901234567890",
			IOS:  "ios-key",
			Demo: "demo-key",
		}

		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)
		middleware := AuthRoleService(apiKeyService, logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer app-key_12345678901234567890123456789012345678901234567890")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("RawError - missing Authorization header", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		authConfig := &config.AuthConfig{App: "app-key", IOS: "ios-key", Demo: "demo-key"}
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		middleware := AuthRoleService(apiKeyService, logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
		assert.Contains(t, w.Body.String(), "error")
	})

	t.Run("RawError - Authorization header without Bearer prefix", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		authConfig := &config.AuthConfig{App: "app-key", IOS: "ios-key", Demo: "demo-key"}
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		middleware := AuthRoleService(apiKeyService, logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Basic dXNlcjpwYXNz")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
		assert.Contains(t, w.Body.String(), "error")
	})

	t.Run("RawError - empty Bearer token", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		authConfig := &config.AuthConfig{App: "app-key", IOS: "ios-key", Demo: "demo-key"}
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		middleware := AuthRoleService(apiKeyService, logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer ")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
		assert.Contains(t, w.Body.String(), "error")
	})

	t.Run("RawError - API key service returns error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		authConfig := &config.AuthConfig{App: "app-key", IOS: "ios-key", Demo: "demo-key"}

		// Mock repository to return error for invalid key
		mockRepo.EXPECT().
			GetServiceIDByAPIKey(gomock.Any(), "invalid-key_12345678901234567890123456789012345678901234567890").
			Return(nil, pgx.ErrNoRows)

		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)
		middleware := AuthRoleService(apiKeyService, logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer invalid-key_12345678901234567890123456789012345678901234567890")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		// When API key is not found, service returns nil service ID and Unknown role with no error
		// But the middleware should still call next handler since it's a valid response
		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("Context values are set correctly", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		svcID := appioid.MustParse("svc_00000000000000000000000001")
		authConfig := &config.AuthConfig{App: "app-key", IOS: "ios-key", Demo: "demo-key"}

		mockRepo.EXPECT().
			GetServiceIDByAPIKey(gomock.Any(), "test-key_12345678901234567890123456789012345678901234567890").
			Return(svcID, nil)

		// Handler that checks context values
		contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()

			// Check service ID in context
			ctxSvcID, ok := GetServiceIDFromContext(ctx)
			assert.True(t, ok)
			assert.Equal(t, svcID, ctxSvcID)

			// Check role in context
			ctxRole, ok := GetRoleFromContext(ctx)
			assert.True(t, ok)
			assert.Equal(t, roles.Api, ctxRole)

			w.WriteHeader(http.StatusOK)
		})

		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)
		middleware := AuthRoleService(apiKeyService, logger)
		handler := middleware(contextCheckHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer test-key_12345678901234567890123456789012345678901234567890")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Context values when service ID is nil", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		authConfig := &config.AuthConfig{
			App:  "app-key",
			IOS:  "ios-key_12345678901234567890123456789012345678901234567890",
			Demo: "demo-key",
		}

		// Handler that checks context values
		contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()

			// Check service ID is not in context
			ctxSvcID, ok := GetServiceIDFromContext(ctx)
			assert.False(t, ok)
			assert.Nil(t, ctxSvcID)

			// Check role in context
			ctxRole, ok := GetRoleFromContext(ctx)
			assert.True(t, ok)
			assert.Equal(t, roles.IOS, ctxRole)

			w.WriteHeader(http.StatusOK)
		})

		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)
		middleware := AuthRoleService(apiKeyService, logger)
		handler := middleware(contextCheckHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer ios-key_12345678901234567890123456789012345678901234567890")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("RawError - API key too short", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		authConfig := &config.AuthConfig{App: "app-key", IOS: "ios-key", Demo: "demo-key"}
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		middleware := AuthRoleService(apiKeyService, logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer short-key") // Too short
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
	})

	t.Run("RawError - database error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		authConfig := &config.AuthConfig{App: "app-key", IOS: "ios-key", Demo: "demo-key"}

		// Mock repository to return database error
		mockRepo.EXPECT().
			GetServiceIDByAPIKey(gomock.Any(), "db-error-key_12345678901234567890123456789012345678901234567890").
			Return(nil, context.DeadlineExceeded)

		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)
		middleware := AuthRoleService(apiKeyService, logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer db-error-key_12345678901234567890123456789012345678901234567890")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
	})
}
