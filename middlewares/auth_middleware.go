package middlewares

import (
	"api.appio.so/helpers"
	"api.appio.so/pkg"
	"api.appio.so/services"
	"context"
	"go.uber.org/zap"
	"net/http"
	"strings"
)

func AuthRoleService(apiKeyService *services.APIKeyService, logger *zap.Logger) func(next http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			auth := r.Header.Get("Authorization")
			if auth == "" || !strings.HasPrefix(auth, "Bearer ") {
				helpers.RenderJSONError(w, r, pkg.ErrUnauthorized)
				return
			}

			apiKey := strings.TrimPrefix(auth, "Bearer ")
			if apiKey == "" {
				helpers.RenderJSONError(w, r, pkg.ErrUnauthorized)
				return
			}

			ctx := r.Context()
			svcID, role, err := apiKeyService.GetServiceIDAndRoleByAPIKey(ctx, apiKey)
			if err != nil {
				helpers.RenderJSONError(w, r, err)
				return
			}

			// Attach to the context
			if svcID != nil {
				ctx = context.WithValue(ctx, SvcIDKey{}, svcID)
			}
			ctx = context.WithValue(ctx, roleKey{}, role)

			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}
