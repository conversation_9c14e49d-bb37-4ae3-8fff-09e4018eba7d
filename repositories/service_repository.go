package repositories

import (
	"context"
	"fmt"
	"strings"

	"api.appio.so/models"
	"github.com/appio-so/go-appioid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type ServiceRepositoryInterface interface {
	FindByID(ctx context.Context, svcID *appioid.ID) (*models.Service, error)
	ListByDeviceID(ctx context.Context, dvcID *appioid.ID) ([]models.Service, error)
	Create(ctx context.Context, svcID *appioid.ID, svcReq models.ServiceRequest) error
	Update(ctx context.Context, svcID *appioid.ID, svcReq models.ServiceRequest) (bool, error)
}

type ServiceRepository struct {
	DB *pgxpool.Pool
}

func NewServiceRepository(db *pgxpool.Pool) *ServiceRepository {
	return &ServiceRepository{
		DB: db,
	}
}

func (r *ServiceRepository) FindByID(ctx context.Context, svcID *appioid.ID) (*models.Service, error) {
	var svc models.Service
	query := "SELECT id, created_at, deactivated_at, title, description, logo_url, banner_url, url, text_color, background_color FROM services WHERE id=@service_id AND deactivated_at IS NULL"
	args := pgx.NamedArgs{
		"service_id": svcID,
	}
	err := r.DB.QueryRow(ctx, query, args).Scan(
		&svc.ID,
		&svc.CreatedAt,
		&svc.DeactivatedAt,
		&svc.Title,
		&svc.Description,
		&svc.LogoURL,
		&svc.BannerURL,
		&svc.URL,
		&svc.TextColor,
		&svc.BackgroundColor,
	)

	if err != nil {
		return nil, fmt.Errorf("finding service: %w", err)
	}
	return &svc, nil
}

func (r *ServiceRepository) ListByDeviceID(ctx context.Context, dvcID *appioid.ID) ([]models.Service, error) {
	query := `
        SELECT s.id, s.user_id, s.created_at, s.deactivated_at, s.title, s.description, s.logo_url, s.banner_url, s.url, s.text_color, s.background_color
        FROM services s
        JOIN dvc_svc ds ON ds.service_id=s.id
        WHERE ds.device_id=@device_id AND ds.deactivated_at IS NULL
		ORDER BY s.created_at DESC`
	args := pgx.NamedArgs{
		"device_id": dvcID,
	}
	return QueryList[models.Service](ctx, r.DB, query, args)
}

func (r *ServiceRepository) Create(ctx context.Context, svcID *appioid.ID, svcReq models.ServiceRequest) error {
	query := `INSERT INTO services (
                          id,
                      	  user_id,
						  title,
                          description,
                          logo_url,
                          banner_url,
                      	  url,
                      	  text_color,
                      	  background_color
				) VALUES (
				          @id,
				          @user_id,
						  @title,
				          @description,
				          @logo_url,
				          @banner_url,
				          @url,
				          @text_color,
				          @background_color
			)`
	args := pgx.NamedArgs{
		"id":               svcID,
		"user_id":          svcReq.UserId,
		"title":            svcReq.Title,
		"description":      svcReq.Description,
		"logo_url":         svcReq.LogoURL,
		"banner_url":       svcReq.BannerURL,
		"url":              svcReq.URL,
		"text_color":       svcReq.TextColor,
		"background_color": svcReq.BackgroundColor,
	}

	_, err := r.DB.Exec(ctx, query, args)
	if err != nil {
		return fmt.Errorf("creating service: %w", err)
	}
	return nil
}

func (r *ServiceRepository) Update(ctx context.Context, svcID *appioid.ID, svcReq models.ServiceRequest) (bool, error) {
	query := "UPDATE services SET"
	args := pgx.NamedArgs{"id": svcID}
	var updateFields []string

	if svcReq.Title != "" {
		updateFields = append(updateFields, "title=@title")
		args["title"] = svcReq.Title
	}
	if svcReq.Description != "" {
		updateFields = append(updateFields, "description=@description")
		args["description"] = svcReq.Description
	}
	if svcReq.BannerURL != "" {
		updateFields = append(updateFields, "banner_url=@banner_url")
		args["banner_url"] = svcReq.BannerURL
	}
	if svcReq.LogoURL != "" {
		updateFields = append(updateFields, "logo_url=@logo_url")
		args["logo_url"] = svcReq.LogoURL
	}
	if svcReq.URL != "" {
		updateFields = append(updateFields, "url=@url")
		args["url"] = svcReq.URL
	}
	if svcReq.TextColor != "" {
		updateFields = append(updateFields, "text_color=@text_color")
		args["text_color"] = strings.ToLower(svcReq.TextColor)
	}
	if svcReq.BackgroundColor != "" {
		updateFields = append(updateFields, "background_color=@background_color")
		args["background_color"] = strings.ToLower(svcReq.BackgroundColor)
	}
	if len(updateFields) == 0 {
		return false, fmt.Errorf("no valid fields to update")
	}
	query += " " + strings.Join(updateFields, ", ") + " WHERE id=@id AND deactivated_at IS NULL"

	cmdTag, err := r.DB.Exec(ctx, query, args)
	if err != nil {
		return false, fmt.Errorf("updating service: %w", err)
	}
	return cmdTag.RowsAffected() > 0, nil
}
