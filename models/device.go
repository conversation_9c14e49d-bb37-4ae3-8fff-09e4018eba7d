package models

import (
	"api.appio.so/pkg"
	"time"

	"github.com/appio-so/go-appioid"
)

const DevicePrefix = "dvc"

type Device struct {
	ID         *appioid.ID
	CreatedAt  time.Time
	LastSeenAt *time.Time
	DeviceData
}

type DeviceCreateRequest struct {
	CustomerUserID string `json:"customer_user_id"`
	DeviceData
}

func (d DeviceCreateRequest) Validate() *pkg.ValidationError {
	var errFields []pkg.ValidationField

	if d.CustomerUserID == "" {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "customer_user_id",
			Reason: "customer_user_id is required",
		})
	}
	if errs := d.DeviceData.Validate(false); errs != nil {
		errFields = append(errFields, errs...)
	}

	if len(errFields) > 0 {
		return &pkg.ValidationError{
			Entity: "device",
			DocUrl: "https://docs.appio.so/#api-devices",
			Fields: errFields,
		}
	}
	return nil
}

type DeviceRecord struct {
	ID             *appioid.ID `json:"id"`
	CustomerUserID string      `json:"user_id"` // NOTE: our users operate with user_id, not customer_user_id
	DeviceData
}

type DeviceResponse struct {
	DeviceRecord
}

type DeviceLinkServiceRequest struct {
	CustomerUserID string `json:"customer_user_id"`
}

// TODO: delete this once ios v1.2 is released
type OldDeviceUpdateRequest struct {
	Data DeviceData `json:"data"`
}

type DeviceUpdateRequest struct {
	Name                 string `json:"name"`
	OsVersion            string `json:"os_version"`
	Model                string `json:"model"`
	DeviceToken          string `json:"device_token"`
	NotificationsEnabled bool   `json:"notifications_enabled"`
	DeviceIdentifier     string `json:"device_identifier"`
	MarketingName        string `json:"marketing_name"`
}

func (d DeviceUpdateRequest) Validate() *pkg.ValidationError {
	var errFields []pkg.ValidationField

	if len(d.Name) > 255 {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "name",
			Reason: "name is too long (max 255 characters)",
		})
	}
	if len(d.OsVersion) > 50 {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "os_version",
			Reason: "os_version is too long (max 50 characters)",
		})
	}
	if len(d.Model) > 100 {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "model",
			Reason: "model is too long (max 100 characters)",
		})
	}
	if len(d.DeviceToken) > 255 {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "device_token",
			Reason: "device_token is too long (max 255 characters)",
		})
	}
	if len(d.DeviceIdentifier) > 50 {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "device_identifier",
			Reason: "device_identifier is too long (max 50 characters)",
		})
	}

	if len(errFields) > 0 {
		return &pkg.ValidationError{
			Entity: "device",
			DocUrl: "https://docs.appio.so/#api-devices",
			Fields: errFields,
		}
	}
	return nil
}

type DeviceData struct {
	Name                 string   `json:"name"`
	Platform             Platform `json:"platform"`
	OsVersion            string   `json:"os_version"`
	Model                string   `json:"model"`
	DeviceToken          string   `json:"device_token"`
	NotificationsEnabled bool     `json:"notifications_enabled"`
	DeviceIdentifier     string   `json:"device_identifier"`
	MarketingName        string   `json:"marketing_name"`
}

// User facing error
func (d DeviceData) Validate(isUpdate bool) []pkg.ValidationField {
	var errFields []pkg.ValidationField

	if !isUpdate && d.Name == "" {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "name",
			Reason: "name is required",
		})
	}
	if len(d.Name) > 255 {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "name",
			Reason: "name is too long (max 255 characters)",
		})
	}
	if !isUpdate {
		if err := d.Platform.Validate(); err != nil {
			errFields = append(errFields, pkg.ValidationField{
				Field:  "platform",
				Reason: err.Error(),
			})
		}
	}
	if !isUpdate && d.OsVersion == "" {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "os_version",
			Reason: "os_version is required",
		})
	}
	if len(d.OsVersion) > 50 {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "os_version",
			Reason: "os_version is too long (max 50 characters)",
		})
	}
	if !isUpdate && d.Model == "" {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "model",
			Reason: "model is required",
		})
	}
	if len(d.Model) > 100 {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "model",
			Reason: "model is too long (max 100 characters)",
		})
	}
	if len(d.DeviceToken) > 255 {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "device_token",
			Reason: "device_token is too long (max 255 characters)",
		})
	}
	if !isUpdate && d.DeviceIdentifier == "" {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "device_identifier",
			Reason: "device_identifier is required",
		})
	}
	if len(d.DeviceIdentifier) > 50 {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "device_identifier",
			Reason: "device_identifier is too long (max 50 characters)",
		})
	}

	return errFields
}
