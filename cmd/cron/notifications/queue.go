package notifications

import (
	"api.appio.so/models"
	"api.appio.so/models/notification_status"
	"context"
	"errors"
	"fmt"
	"github.com/appio-so/go-appioid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
	"time"
)

const (
	queueTimeout = 10 * time.Second

	// Select notifications in a `created` state with row-level locking
	// Select all queued regardless of deactivated service or device. This will be checked during delivery
	selectNotificationsQuery = `
		SELECT
			n.id,
			n.service_id,
			n.created_at,
			n.scheduled_at,
			n.type,
			n.status,
			n.payload
		FROM notifications n
		JOIN services s ON s.id=n.service_id
		WHERE n.status=@status AND n.scheduled_at <= NOW()
		ORDER BY n.created_at ASC
		FOR UPDATE SKIP LOCKED`

	selectDeactivatedServiceQuery = `
		SELECT EXISTS (
			SELECT 1 FROM services
			WHERE id=@id AND deactivated_at IS NOT NULL
		)`

	selectDevicesQuery = `
		SELECT d.id, d.device_token 
		FROM devices d
		JOIN dvc_svc ds ON ds.device_id=d.id 
			AND ds.deactivated_at IS NULL 
			AND ds.service_id=@service_id
		JOIN services s ON s.id=ds.service_id 
			AND s.deactivated_at IS NULL
		WHERE d.notifications_enabled=true
			AND d.device_token IS NOT NULL AND d.device_token <> ''
			AND NOT EXISTS (
				SELECT 1 
				FROM notification_deliveries nd 
				WHERE nd.device_id=d.id
					AND nd.notification_id=@notification_id
			)`

	insertDeliveryQuery = `
		INSERT INTO notification_deliveries (id, notification_id, device_id, status)
		VALUES (@id, @notification_id, @device_id, @status)`

	updateNotificationQuery = `
		UPDATE notifications SET status=@status WHERE id=@id`
)

type DeviceIdToken struct {
	ID          *appioid.ID `json:"id"`
	DeviceToken *string     `json:"device_token"`
}

func Queue(db *pgxpool.Pool, logger *zap.Logger) {
	if err := queueNotifications(db, logger); err != nil {
		logger.Error("queuing notifications error", zap.Error(err))
	}
}

func queueNotifications(db *pgxpool.Pool, logger *zap.Logger) error {
	ctx, cancel := context.WithTimeout(context.Background(), queueTimeout)
	defer cancel()

	tx, err := db.Begin(ctx)
	if err != nil {
		return fmt.Errorf("begin tx error: %w", err)
	}
	defer tx.Rollback(context.Background())

	notifications, err := fetchNotifications(ctx, tx)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			logger.Info("no notifications found to queue")
			return nil
		}
		return fmt.Errorf("fetch notifications: %w", err)
	}

	if err := processNotifications(ctx, tx, notifications, logger); err != nil {
		return err
	}

	if err := tx.Commit(ctx); err != nil {
		return fmt.Errorf("commit transaction: %w", err)
	}

	return nil
}

func fetchNotifications(ctx context.Context, tx pgx.Tx) ([]models.Notification, error) {
	args := pgx.NamedArgs{"status": notification_status.Created}
	return queryList[models.Notification](ctx, tx, selectNotificationsQuery, args)
}

func processNotifications(ctx context.Context, tx pgx.Tx, notifications []models.Notification, logger *zap.Logger) error {
	for _, notification := range notifications {
		logger.Info("processing notification", zap.String("id", notification.ID.String()))
		if err := processNotification(ctx, tx, notification, logger); err != nil {
			return err
		}
	}
	return nil
}

func processNotification(ctx context.Context, tx pgx.Tx, notification models.Notification, logger *zap.Logger) error {
	if isServiceDeactivated(ctx, tx, notification.ServiceID.String()) {
		return markNotification(ctx, tx, notification.ID, notification_status.Skipped)
	}

	devices, err := listDevicesForNotificationByService(ctx, tx, notification.ServiceID, notification.ID)
	if err != nil {
		return fmt.Errorf("list devices: %w", err)
	}

	for _, device := range devices {
		if err := processDevice(ctx, tx, device, notification, logger); err != nil {
			return err
		}
	}

	return markNotification(ctx, tx, notification.ID, notification_status.Queued)
}

func processDevice(ctx context.Context, tx pgx.Tx, device DeviceIdToken, notification models.Notification, logger *zap.Logger) error {
	logger.Info("queuing notification for device", zap.String("id", device.ID.String()))

	ntfDlvID, err := appioid.New(models.NotificationDeliveryPrefix)
	if err != nil {
		return fmt.Errorf("create notification delivery id: %w", err)
	}

	if err := createNotificationDelivery(ctx, tx, ntfDlvID, notification.ID, device.ID); err != nil {
		var pgErr *pgconn.PgError
		if errors.As(err, &pgErr) && pgErr.Code == "23505" && pgErr.ConstraintName == "uniq_active_dvc_svc" {
			logger.Warn("Notification delivery already exists",
				zap.String("device_id", device.ID.String()),
				zap.String("notification_id", notification.ID.String()))
			return nil
		}
		return err
	}
	return nil
}

func listDevicesForNotificationByService(ctx context.Context, tx pgx.Tx, svcID, ntfID *appioid.ID) ([]DeviceIdToken, error) {
	args := pgx.NamedArgs{
		"service_id":      svcID,
		"notification_id": ntfID,
	}
	return queryList[DeviceIdToken](ctx, tx, selectDevicesQuery, args)
}

func createNotificationDelivery(ctx context.Context, tx pgx.Tx, ntfDlvID, ntfID, dvcID *appioid.ID) error {
	args := pgx.NamedArgs{
		"id":              ntfDlvID,
		"notification_id": ntfID,
		"device_id":       dvcID,
		"status":          notification_status.Queued,
	}

	if _, err := tx.Exec(ctx, insertDeliveryQuery, args); err != nil {
		return fmt.Errorf("create notification delivery: %w", err)
	}
	return nil
}

func markNotification(ctx context.Context, tx pgx.Tx, ntfID *appioid.ID, status notification_status.Status) error {
	args := pgx.NamedArgs{
		"status": status,
		"id":     ntfID,
	}
	if _, err := tx.Exec(ctx, updateNotificationQuery, args); err != nil {
		return fmt.Errorf("mark notification %s: %w", status, err)
	}
	return nil
}

// Returns empty slice if no rows found (not error)
func queryList[T any](ctx context.Context, tx pgx.Tx, query string, args ...any) ([]T, error) {
	rows, err := tx.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("execute query: %w", err)
	}
	defer rows.Close()

	list, err := pgx.CollectRows(rows, pgx.RowToStructByName[T])
	if err != nil {
		return nil, fmt.Errorf("collect rows: %w", err)
	}
	return list, nil
}

func isServiceDeactivated(ctx context.Context, tx pgx.Tx, svcID string) bool {
	args := pgx.NamedArgs{"id": svcID}
	var deactivated bool
	if err := tx.QueryRow(ctx, selectDeactivatedServiceQuery, args).Scan(&deactivated); err != nil {
		return false
	}
	return deactivated
}
