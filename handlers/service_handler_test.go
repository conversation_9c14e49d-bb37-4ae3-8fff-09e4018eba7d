package handlers

import (
	"api.appio.so/models"
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"api.appio.so/internal/mocks"
	"api.appio.so/middlewares"
	"api.appio.so/pkg"
	"github.com/appio-so/go-appioid"
	"github.com/go-chi/chi/v5"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"go.uber.org/zap/zaptest"
)

func setupServiceHandler(t *testing.T) (*ServiceHandler, *mocks.MockServiceServiceInterface, *gomock.Controller) {
	ctrl := gomock.NewController(t)
	mockService := mocks.NewMockServiceServiceInterface(ctrl)
	logger := zaptest.NewLogger(t)
	handler := NewServiceHandler(mockService, logger)
	return handler, mockService, ctrl
}

func addServiceIDToServiceContext(req *http.Request, svcID *appioid.ID) *http.Request {
	ctx := context.WithValue(req.Context(), middlewares.SvcIDKey{}, svcID)
	return req.WithContext(ctx)
}

func addPlatformToServiceContext(req *http.Request, platform models.Platform) *http.Request {
	ctx := context.WithValue(req.Context(), middlewares.PlatformKey{}, platform)
	return req.WithContext(ctx)
}

func TestNewServiceHandler(t *testing.T) {
	t.Run("Creates service handler correctly", func(t *testing.T) {
		handler, _, ctrl := setupServiceHandler(t)
		defer ctrl.Finish()

		assert.NotNil(t, handler)
		assert.NotNil(t, handler.service)
		assert.NotNil(t, handler.logger)
	})
}

func TestServiceHandler_Get(t *testing.T) {
	svcID := appioid.MustParse("svc_00000000000000000000000001")

	t.Run("Success - service found", func(t *testing.T) {
		handler, mockService, ctrl := setupServiceHandler(t)
		defer ctrl.Finish()

		expectedService := &models.Service{
			ID: svcID,
			ServiceRequest: models.ServiceRequest{
				Title: "Test Service",
			},
		}

		mockService.EXPECT().
			FindByID(gomock.Any(), svcID).
			Return(expectedService, nil)

		router := chi.NewRouter()
		router.Get("/services/{id}", handler.Get)

		req := httptest.NewRequest("GET", "/services/"+svcID.String(), nil)
		req = addServiceIDToServiceContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		var response models.Service
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, svcID, response.ID)
		assert.Equal(t, "Test Service", response.ServiceRequest.Title)
	})

	t.Run("RawError - service not found", func(t *testing.T) {
		handler, mockService, ctrl := setupServiceHandler(t)
		defer ctrl.Finish()

		mockService.EXPECT().
			FindByID(gomock.Any(), svcID).
			Return(nil, nil)

		router := chi.NewRouter()
		router.Get("/services/{id}", handler.Get)

		req := httptest.NewRequest("GET", "/services/"+svcID.String(), nil)
		req = addServiceIDToServiceContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("RawError - service error", func(t *testing.T) {
		handler, mockService, ctrl := setupServiceHandler(t)
		defer ctrl.Finish()

		mockService.EXPECT().
			FindByID(gomock.Any(), svcID).
			Return(nil, pkg.ErrInternal)

		router := chi.NewRouter()
		router.Get("/services/{id}", handler.Get)

		req := httptest.NewRequest("GET", "/services/"+svcID.String(), nil)
		req = addServiceIDToServiceContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("RawError - missing service ID", func(t *testing.T) {
		handler, _, ctrl := setupServiceHandler(t)
		defer ctrl.Finish()

		router := chi.NewRouter()
		router.Get("/services/{id}", handler.Get)

		req := httptest.NewRequest("GET", "/services/"+svcID.String(), nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("RawError - context service ID mismatch", func(t *testing.T) {
		handler, _, ctrl := setupServiceHandler(t)
		defer ctrl.Finish()

		differentSvcID := appioid.MustParse("svc_00000000000000000000000002")

		router := chi.NewRouter()
		router.Get("/services/{id}", handler.Get)

		req := httptest.NewRequest("GET", "/services/"+svcID.String(), nil)
		req = addServiceIDToServiceContext(req, differentSvcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

func TestServiceHandler_GetWithWidgets(t *testing.T) {
	svcID := appioid.MustParse("svc_00000000000000000000000001")

	t.Run("Success - service with widgets found", func(t *testing.T) {
		handler, mockService, ctrl := setupServiceHandler(t)
		defer ctrl.Finish()

		expectedServiceWithWidgets := &models.ServiceWithWidgets{
			Service: &models.Service{
				ID: svcID,
				ServiceRequest: models.ServiceRequest{
					Title: "Test Service",
				},
			},
			Widgets: []models.Widget{
				{ID: appioid.MustParse("wgt_00000000000000000000000001")},
			},
		}

		mockService.EXPECT().
			FindByIDWithWidgets(gomock.Any(), svcID).
			Return(expectedServiceWithWidgets, nil)

		router := chi.NewRouter()
		router.Get("/services/{id}/widgets", handler.GetWithWidgets)

		req := httptest.NewRequest("GET", "/services/"+svcID.String()+"/widgets", nil)
		req = addServiceIDToServiceContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		var response models.ServiceWithWidgets
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, svcID, response.Service.ID)
		assert.Len(t, response.Widgets, 1)
	})

	t.Run("RawError - service not found", func(t *testing.T) {
		handler, mockService, ctrl := setupServiceHandler(t)
		defer ctrl.Finish()

		mockService.EXPECT().
			FindByIDWithWidgets(gomock.Any(), svcID).
			Return(nil, nil)

		router := chi.NewRouter()
		router.Get("/services/{id}/widgets", handler.GetWithWidgets)

		req := httptest.NewRequest("GET", "/services/"+svcID.String()+"/widgets", nil)
		req = addServiceIDToServiceContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("RawError - service error", func(t *testing.T) {
		handler, mockService, ctrl := setupServiceHandler(t)
		defer ctrl.Finish()

		mockService.EXPECT().
			FindByIDWithWidgets(gomock.Any(), svcID).
			Return(nil, pkg.ErrInternal)

		router := chi.NewRouter()
		router.Get("/services/{id}/widgets", handler.GetWithWidgets)

		req := httptest.NewRequest("GET", "/services/"+svcID.String()+"/widgets", nil)
		req = addServiceIDToServiceContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})
}

func TestServiceHandler_GetWithWidgetConfigs(t *testing.T) {
	svcID := appioid.MustParse("svc_00000000000000000000000001")

	t.Run("Success - service with widget configs found", func(t *testing.T) {
		handler, mockService, ctrl := setupServiceHandler(t)
		defer ctrl.Finish()

		expectedServiceWithWidgetConfigs := &models.ServiceWithWidgetConfigs{
			Service: &models.Service{
				ID: svcID,
				ServiceRequest: models.ServiceRequest{
					Title: "Test Service",
				},
			},
			WidgetConfigs: []models.WidgetConfig{
				{ID: appioid.MustParse("wgt_00000000000000000000000001")},
			},
		}

		mockService.EXPECT().
			FindByIDWithWidgetConfigs(gomock.Any(), models.PlatformIOS, svcID).
			Return(expectedServiceWithWidgetConfigs, nil)

		router := chi.NewRouter()
		router.Get("/services/{id}/widget-configs", handler.GetWithWidgetConfigs)

		req := httptest.NewRequest("GET", "/services/"+svcID.String()+"/widget-configs", nil)
		req = addServiceIDToServiceContext(req, svcID)
		req = addPlatformToServiceContext(req, models.PlatformIOS)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		var response models.ServiceWithWidgetConfigs
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, svcID, response.Service.ID)
		assert.Len(t, response.WidgetConfigs, 1)
	})

	t.Run("RawError - service not found", func(t *testing.T) {
		handler, mockService, ctrl := setupServiceHandler(t)
		defer ctrl.Finish()

		mockService.EXPECT().
			FindByIDWithWidgetConfigs(gomock.Any(), models.PlatformIOS, svcID).
			Return(nil, nil)

		router := chi.NewRouter()
		router.Get("/services/{id}/widget-configs", handler.GetWithWidgetConfigs)

		req := httptest.NewRequest("GET", "/services/"+svcID.String()+"/widget-configs", nil)
		req = addServiceIDToServiceContext(req, svcID)
		req = addPlatformToServiceContext(req, models.PlatformIOS)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("RawError - service error", func(t *testing.T) {
		handler, mockService, ctrl := setupServiceHandler(t)
		defer ctrl.Finish()

		mockService.EXPECT().
			FindByIDWithWidgetConfigs(gomock.Any(), models.PlatformIOS, svcID).
			Return(nil, pkg.ErrInternal)

		router := chi.NewRouter()
		router.Get("/services/{id}/widget-configs", handler.GetWithWidgetConfigs)

		req := httptest.NewRequest("GET", "/services/"+svcID.String()+"/widget-configs", nil)
		req = addServiceIDToServiceContext(req, svcID)
		req = addPlatformToServiceContext(req, models.PlatformIOS)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})
}

func TestServiceHandler_CreateDemo(t *testing.T) {
	svcID := appioid.MustParse("demo_00000000000000000000000001")

	t.Run("Success - demo service created", func(t *testing.T) {
		handler, mockService, ctrl := setupServiceHandler(t)
		defer ctrl.Finish()

		serviceReq := models.ServiceRequest{
			UserId:          "demo_user_123",
			Title:           "Demo Service",
			LogoURL:         "https://example.com/logo.png",
			TextColor:       "#000000",
			BackgroundColor: "#FFffFF",
		}

		mockService.EXPECT().
			Create(gomock.Any(), serviceReq, "demo_svc").
			Return(svcID, nil)

		reqBody, _ := json.Marshal(serviceReq)
		req := httptest.NewRequest("POST", "/services/demo", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		handler.CreateDemo(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		var response models.ResponseID
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, svcID, response.ID)
	})

	t.Run("RawError - invalid JSON", func(t *testing.T) {
		handler, _, ctrl := setupServiceHandler(t)
		defer ctrl.Finish()

		req := httptest.NewRequest("POST", "/services/demo", bytes.NewBuffer([]byte("invalid json")))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		handler.CreateDemo(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("RawError - service error", func(t *testing.T) {
		handler, mockService, ctrl := setupServiceHandler(t)
		defer ctrl.Finish()

		serviceReq := models.ServiceRequest{
			UserId:          "demo_user_123",
			Title:           "Demo Service",
			LogoURL:         "https://example.com/logo.png",
			TextColor:       "#000000",
			BackgroundColor: "#FFffFF",
		}

		mockService.EXPECT().
			Create(gomock.Any(), serviceReq, "demo_svc").
			Return(nil, pkg.ErrInternal)

		reqBody, _ := json.Marshal(serviceReq)
		req := httptest.NewRequest("POST", "/services/demo", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		handler.CreateDemo(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})
}

func TestServiceHandler_Update(t *testing.T) {
	svcID := appioid.MustParse("svc_00000000000000000000000001")

	t.Run("Success - service updated", func(t *testing.T) {
		handler, mockService, ctrl := setupServiceHandler(t)
		defer ctrl.Finish()

		serviceReq := models.ServiceRequest{
			Title: "Updated Service",
		}

		mockService.EXPECT().
			Update(gomock.Any(), svcID, serviceReq).
			Return(nil)

		reqBody, _ := json.Marshal(serviceReq)
		router := chi.NewRouter()
		router.Put("/services/{id}", handler.Update)

		req := httptest.NewRequest("PUT", "/services/"+svcID.String(), bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req = addServiceIDToServiceContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		var response models.ResponseID
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, svcID, response.ID)
	})

	t.Run("RawError - invalid JSON", func(t *testing.T) {
		handler, _, ctrl := setupServiceHandler(t)
		defer ctrl.Finish()

		router := chi.NewRouter()
		router.Put("/services/{id}", handler.Update)

		req := httptest.NewRequest("PUT", "/services/"+svcID.String(), bytes.NewBuffer([]byte("invalid json")))
		req.Header.Set("Content-Type", "application/json")
		req = addServiceIDToServiceContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("RawError - service error", func(t *testing.T) {
		handler, mockService, ctrl := setupServiceHandler(t)
		defer ctrl.Finish()

		serviceReq := models.ServiceRequest{
			Title: "Updated Service",
		}

		mockService.EXPECT().
			Update(gomock.Any(), svcID, serviceReq).
			Return(pkg.ErrInternal)

		reqBody, _ := json.Marshal(serviceReq)
		router := chi.NewRouter()
		router.Put("/services/{id}", handler.Update)

		req := httptest.NewRequest("PUT", "/services/"+svcID.String(), bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req = addServiceIDToServiceContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("RawError - missing service ID", func(t *testing.T) {
		handler, _, ctrl := setupServiceHandler(t)
		defer ctrl.Finish()

		serviceReq := models.ServiceRequest{
			Title: "Updated Service",
		}

		reqBody, _ := json.Marshal(serviceReq)
		router := chi.NewRouter()
		router.Put("/services/{id}", handler.Update)

		req := httptest.NewRequest("PUT", "/services/"+svcID.String(), bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("RawError - context service ID mismatch", func(t *testing.T) {
		handler, _, ctrl := setupServiceHandler(t)
		defer ctrl.Finish()

		differentSvcID := appioid.MustParse("svc_00000000000000000000000002")
		serviceReq := models.ServiceRequest{
			Title: "Updated Service",
		}

		reqBody, _ := json.Marshal(serviceReq)
		router := chi.NewRouter()
		router.Put("/services/{id}", handler.Update)

		req := httptest.NewRequest("PUT", "/services/"+svcID.String(), bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req = addServiceIDToServiceContext(req, differentSvcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}
