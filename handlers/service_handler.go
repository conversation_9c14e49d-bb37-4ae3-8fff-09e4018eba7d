package handlers

import (
	"fmt"
	"net/http"

	"api.appio.so/helpers"
	"api.appio.so/models"
	"api.appio.so/pkg"
	"api.appio.so/services"
	"github.com/appio-so/go-appioid"
	"go.uber.org/zap"
)

type ServiceHandler struct {
	Handler

	service services.ServiceServiceInterface
}

func NewServiceHandler(serviceService services.ServiceServiceInterface, logger *zap.Logger) *ServiceHandler {
	return &ServiceHandler{
		Handler: Handler{logger: logger},
		service: serviceService,
	}
}

func (h *ServiceHandler) List(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	list, err := h.service.List(ctx)
	if err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}

	helpers.RenderJSON(w, r, list, http.StatusOK)
}

func (h *ServiceHandler) ListByDeviceWithWidgetConfigs(w http.ResponseWriter, r *http.Request) {
	platform, ok := h.GetPlatform(w, r)
	if !ok {
		return
	}

	dvcID, ok := h.GetDeviceID(w, r)
	if !ok {
		return
	}

	ctx := r.Context()
	devices, err := h.service.ListByDeviceWithWidgetConfigs(ctx, platform, dvcID)
	if err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}

	helpers.RenderJSON(w, r, devices, http.StatusOK)
}

func (h *ServiceHandler) Get(w http.ResponseWriter, r *http.Request) {
	svcID, ok := h.GetServiceID(w, r)
	if !ok {
		return
	}
	if !h.validateContextServiceIDAgainstURLParam(svcID, w, r) {
		return
	}

	ctx := r.Context()
	service, err := h.service.FindByID(ctx, svcID)
	if err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}

	if service == nil {
		helpers.RenderJSONError(w, r, pkg.ErrNotFound)
	} else {
		helpers.RenderJSON(w, r, service, http.StatusOK)
	}
}

func (h *ServiceHandler) GetWithWidgets(w http.ResponseWriter, r *http.Request) {
	svcID, ok := h.GetServiceID(w, r)
	if !ok {
		return
	}
	if !h.validateContextServiceIDAgainstURLParam(svcID, w, r) {
		return
	}

	ctx := r.Context()
	serviceWithWidgetConfigs, err := h.service.FindByIDWithWidgets(ctx, svcID)
	if err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}
	if serviceWithWidgetConfigs == nil {
		helpers.RenderJSONError(w, r, pkg.ErrNotFound)
	} else {
		helpers.RenderJSON(w, r, serviceWithWidgetConfigs, http.StatusOK)
	}
}

func (h *ServiceHandler) GetWithWidgetConfigs(w http.ResponseWriter, r *http.Request) {
	platform, ok := h.GetPlatform(w, r)
	if !ok {
		return
	}

	svcID, ok := h.GetServiceID(w, r)
	if !ok {
		return
	}
	if !h.validateContextServiceIDAgainstURLParam(svcID, w, r) {
		return
	}

	ctx := r.Context()
	serviceWithWidgets, err := h.service.FindByIDWithWidgetConfigs(ctx, platform, svcID)
	if err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}
	if serviceWithWidgets == nil {
		helpers.RenderJSONError(w, r, pkg.ErrNotFound)
	} else {
		helpers.RenderJSON(w, r, serviceWithWidgets, http.StatusOK)
	}
}

func (h *ServiceHandler) CreateDemo(w http.ResponseWriter, r *http.Request) {
	svcReq, err := helpers.DecodeJSON[models.ServiceRequest](r)
	if err != nil {
		helpers.RenderJSONError(w, r, pkg.ErrInvalidInput)
		return
	}

	if verr := svcReq.Validate(false); verr != nil {
		h.logger.Debug("invalid service create request", zap.Any("data", svcReq), zap.Error(verr))
		helpers.RenderJSONError(w, r, pkg.NewUserFacingError(pkg.ErrInvalidInput, verr.Data()))
		return
	}

	ctx := r.Context()
	svcID, err := h.service.Create(ctx, svcReq, fmt.Sprintf("demo_%s", models.ServicePrefix))
	if err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}

	helpers.RenderJSON(w, r, &models.ResponseID{ID: svcID}, http.StatusCreated)
}

func (h *ServiceHandler) Update(w http.ResponseWriter, r *http.Request) {
	svcID, ok := h.GetServiceID(w, r)
	if !ok {
		return
	}
	if !h.validateContextServiceIDAgainstURLParam(svcID, w, r) {
		return
	}

	svcReq, err := helpers.DecodeJSON[models.ServiceRequest](r)
	if err != nil {
		helpers.RenderJSONError(w, r, pkg.ErrInvalidInput)
		return
	}

	if verr := svcReq.Validate(true); verr != nil {
		h.logger.Debug("invalid service update request", zap.Any("data", svcReq), zap.Error(verr))
		helpers.RenderJSONError(w, r, pkg.NewUserFacingError(pkg.ErrInvalidInput, verr.Data()))
		return
	}

	ctx := r.Context()
	if err := h.service.Update(ctx, svcID, svcReq); err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}

	helpers.RenderJSON(w, r, &models.ResponseID{ID: svcID}, http.StatusOK)
}

// --------------------------------------------------------------------------------------------------------------------

func (h *ServiceHandler) validateContextServiceIDAgainstURLParam(svcID *appioid.ID, w http.ResponseWriter, r *http.Request) bool {
	urlSvcID, ok := h.GetUrlID(w, r)
	if !ok || svcID == nil || urlSvcID.String() != svcID.String() {
		helpers.RenderJSONError(w, r, pkg.ErrInvalidInput)
		return false
	}
	return true
}
