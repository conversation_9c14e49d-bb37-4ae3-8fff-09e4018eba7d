package services

import (
	"api.appio.so/pkg/config"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
	"testing"
)

func TestNewServiceContainer(t *testing.T) {
	t.Run("Creates service container with all services", func(t *testing.T) {
		// Create test dependencies
		db := &pgxpool.Pool{}
		dbFing := &pgxpool.Pool{}
		cfg := &config.Config{
			Auth: config.AuthConfig{
				App:  "test-app-key",
				IOS:  "test-ios-key",
				Demo: "test-demo-key",
			},
			Server: config.ServerConfig{
				Env: "test",
			},
		}
		logger := zap.NewNop()

		// Create service container
		container := NewServiceContainer(db, dbFing, cfg, logger)

		// Verify all services are initialized
		assert.NotNil(t, container)
		assert.NotNil(t, container.APIKeyService)
		assert.NotNil(t, container.ServiceService)
		assert.NotNil(t, container.DeviceService)
		assert.NotNil(t, container.NotificationService)
		assert.NotNil(t, container.WidgetService)
		assert.NotNil(t, container.WidgetConfigService)
		assert.NotNil(t, container.FeatureFlagService)
		assert.NotNil(t, container.FingerprintService)

		// Verify service dependencies are properly set
		assert.Equal(t, logger, container.APIKeyService.logger)
		assert.Equal(t, logger, container.ServiceService.logger)
		assert.Equal(t, logger, container.DeviceService.logger)
		assert.Equal(t, logger, container.NotificationService.logger)
		assert.Equal(t, logger, container.WidgetService.logger)
		assert.Equal(t, logger, container.WidgetConfigService.logger)
		assert.Equal(t, logger, container.FeatureFlagService.logger)
		assert.Equal(t, logger, container.FingerprintService.logger)

		// Verify fingerprint service is properly initialized
		assert.NotNil(t, container.FingerprintService)
	})

	t.Run("Creates service container with nil parameters", func(t *testing.T) {
		// This tests the robustness of the constructor
		container := NewServiceContainer(nil, nil, &config.Config{}, nil)

		// Should still create the container (though services may not work properly)
		assert.NotNil(t, container)
		assert.NotNil(t, container.APIKeyService)
		assert.NotNil(t, container.ServiceService)
		assert.NotNil(t, container.DeviceService)
		assert.NotNil(t, container.NotificationService)
		assert.NotNil(t, container.WidgetService)
		assert.NotNil(t, container.WidgetConfigService)
		assert.NotNil(t, container.FeatureFlagService)
		assert.NotNil(t, container.FingerprintService)
	})

	t.Run("Creates service container with minimal config", func(t *testing.T) {
		db := &pgxpool.Pool{}
		dbFing := &pgxpool.Pool{}
		cfg := &config.Config{} // Minimal config
		logger := zap.NewNop()

		container := NewServiceContainer(db, dbFing, cfg, logger)

		assert.NotNil(t, container)
		// All services should still be created even with minimal config
		assert.NotNil(t, container.APIKeyService)
		assert.NotNil(t, container.ServiceService)
		assert.NotNil(t, container.DeviceService)
		assert.NotNil(t, container.NotificationService)
		assert.NotNil(t, container.WidgetService)
		assert.NotNil(t, container.WidgetConfigService)
		assert.NotNil(t, container.FeatureFlagService)
		assert.NotNil(t, container.FingerprintService)
	})
}
